export interface Tier {
  name: string;
  id: 'free' | 'pro' | 'lifetime';
  icon: string;
  description: string;
  features: string[];
  featured: boolean;
  priceId: Record<string, string>;
  price: Record<string, number>;
  planId: Record<string, string>; // UUID from your plans table
}

export const PricingTier: Tier[] = [
  {
    name: 'Free',
    id: 'free',
    icon: '/assets/icons/price-tiers/free-icon.svg',
    description: 'Perfect for getting started with basic features at no cost.',
    features: ['Basic features', 'Limited usage', 'Community support'],
    featured: false,
    priceId: { lifetime: '' }, // No Paddle price ID for free plan
    price: { lifetime: 0 },
    planId: { lifetime: '4f1bae2c-86b7-4cd0-8a52-c86c82520857' },
  },
  {
    name: 'Pro',
    id: 'pro',
    icon: '/assets/icons/price-tiers/basic-icon.svg',
    description: 'Enhanced features for professionals who need more flexibility.',
    features: ['All Free features', 'Advanced tools', 'Priority support', 'Monthly or yearly billing'],
    featured: true,
    priceId: {
      month: 'pri_01jza23mbtxwejg1ma9a5nrf1h',
      year: 'pri_01jza24jn0tvzd49c9dnmt36c2'
    },
    price: { month: 5, year: 49 },
    planId: {
      month: 'c22f00f3-6521-456a-a606-cb8779d64893',
      year: '49827051-f0e6-4a72-9590-e195a0eed854'
    },
  },
  {
    name: 'Lifetime',
    id: 'lifetime',
    icon: '/assets/icons/price-tiers/pro-icon.svg',
    description: 'One-time payment for lifetime access to all premium features.',
    features: [
      'All Pro features',
      'Lifetime access',
      'No recurring payments',
      'Future updates included',
      'Premium support',
    ],
    featured: false,
    priceId: { lifetime: 'pri_01jz9z6zg654q1rfra04vsxdsb' },
    price: { lifetime: 99 },
    planId: { lifetime: '4f2dda57-3e45-4175-b4f8-cb406c25556c' },
  },
];
