import { NextRequest } from 'next/server';
import { createClient } from '@/utils/supabase/server-internal';

export async function POST(request: NextRequest) {
  console.log('🧪 Testing subscription creation...');
  
  try {
    const supabase = await createClient();
    const body = await request.json();
    
    console.log('📝 Request body:', body);
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.log('❌ No authenticated user');
      return Response.json({ error: 'No authenticated user' }, { status: 401 });
    }
    
    console.log('👤 Current user:', user.email);
    
    // Check if user has a profile
    const { data: profile, error: profileError } = await supabase
      .from('profile')
      .select('*')
      .eq('user_id', user.id)
      .single();
    
    console.log('👤 User profile:', { profile, profileError });
    
    if (profileError || !profile) {
      // Create profile if it doesn't exist
      console.log('🆕 Creating user profile...');
      const { data: newProfile, error: createProfileError } = await supabase
        .from('profile')
        .insert({
          user_id: user.id,
          email: user.email!,
          username: user.email!.split('@')[0],
        })
        .select()
        .single();
      
      console.log('🆕 Profile creation result:', { newProfile, createProfileError });
      
      if (createProfileError) {
        return Response.json({ error: 'Failed to create profile', details: createProfileError }, { status: 500 });
      }
    }
    
    // Get a plan to test with
    const { data: plans, error: plansError } = await supabase
      .from('plans')
      .select('*')
      .eq('name', 'free')
      .single();
    
    console.log('📋 Free plan:', { plans, plansError });
    
    if (plansError || !plans) {
      return Response.json({ error: 'No free plan found', details: plansError }, { status: 404 });
    }
    
    // Create test subscription
    const subscriptionData = {
      user_id: user.id,
      plan_id: plans.id,
      paddle_subscription_id: `test_${Date.now()}`,
      status: 'active',
      start_date: new Date().toISOString(),
      end_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 year from now
      updated_at: new Date().toISOString(),
    };
    
    console.log('💾 Creating subscription with data:', subscriptionData);
    
    const { data: subscription, error: subscriptionError } = await supabase
      .from('subscriptions')
      .insert(subscriptionData)
      .select()
      .single();
    
    console.log('💳 Subscription creation result:', { subscription, subscriptionError });
    
    if (subscriptionError) {
      return Response.json({ 
        error: 'Failed to create subscription', 
        details: subscriptionError,
        subscriptionData 
      }, { status: 500 });
    }
    
    return Response.json({
      success: true,
      subscription,
      user: user.email,
      plan: plans.name,
      timestamp: new Date().toISOString(),
    });
    
  } catch (error) {
    console.error('💥 Test subscription error:', error);
    return Response.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}
