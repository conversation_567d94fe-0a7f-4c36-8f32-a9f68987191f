import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { SubscriptionCards } from '@/components/dashboard/subscriptions/components/subscription-cards';
import { getUserSubscriptions } from '@/utils/supabase/subscriptions';
import { ErrorContent } from '@/components/dashboard/layout/error-content';

export async function DashboardSubscriptionCardGroup() {
  const { data: subscriptions, error } = await getUserSubscriptions();

  return (
    <Card className={'bg-background/50 backdrop-blur-[24px] border-border p-6'}>
      <CardHeader className="p-0 space-y-0">
        <CardTitle className="flex justify-between items-center pb-6 border-border border-b">
          <span className={'text-xl font-medium'}>Active subscriptions</span>
          <Button asChild={true} size={'sm'} variant={'outline'} className={'text-sm rounded-sm border-border'}>
            <Link href={'/dashboard/subscriptions'}>View all</Link>
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className={'p-0 pt-6 @container'}>
        {!error && subscriptions && subscriptions.length > 0 ? (
          <SubscriptionCards
            className={'grid-cols-1 gap-6 @[600px]:grid-cols-2'}
            subscriptions={subscriptions.map((sub) => ({
              id: sub.id,
              status: sub.status,
              items: [
                {
                  price: {
                    name: sub.plans?.name || 'Unknown Plan',
                    description: sub.plans?.discription || '',
                  },
                },
              ],
              startedAt: sub.start_date,
              nextBilledAt: sub.end_date,
            }))}
          />
        ) : (
          <ErrorContent />
        )}
      </CardContent>
    </Card>
  );
}
