'use client';

import { useSubscription } from '@/hooks/useSubscriptions';
import { SubscriptionPastPaymentsCard } from '@/components/dashboard/subscriptions/components/subscription-past-payments-card';
import { SubscriptionNextPaymentCard } from '@/components/dashboard/subscriptions/components/subscription-next-payment-card';
import { SubscriptionLineItems } from '@/components/dashboard/subscriptions/components/subscription-line-items';
import { SubscriptionHeader } from '@/components/dashboard/subscriptions/components/subscription-header';
import { Separator } from '@/components/ui/separator';
import { ErrorContent } from '@/components/dashboard/layout/error-content';
import { LoadingScreen } from '@/components/dashboard/layout/loading-screen';

interface Props {
  subscriptionId: string;
}

export function SubscriptionDetail({ subscriptionId }: Props) {
  const { subscription, loading, error } = useSubscription(subscriptionId);

  if (loading) {
    return <LoadingScreen />;
  }

  if (error || !subscription) {
    return <ErrorContent />;
  }

  // Create a Paddle-like object from our database data for compatibility with existing components
  const displaySubscription = {
    id: subscription.paddle_subscription_id || subscription.id,
    status: subscription.status,
    items: [
      {
        price: {
          name: subscription.plans?.name || 'Unknown Plan',
          description: subscription.plans?.discription || '',
        },
        quantity: 1,
      },
    ],
    currencyCode: 'USD', // You might want to store this in your plans table
    billingCycle: {
      interval:
        subscription.plans?.interval === 'monthly'
          ? 'month'
          : subscription.plans?.interval === 'yearly'
            ? 'year'
            : 'lifetime',
      frequency: 1,
    },
    startedAt: subscription.start_date,
    nextBilledAt: subscription.end_date,
    recurringTransactionDetails: {
      totals: {
        total: ((subscription.plans?.price || 0) * 100).toString(), // Convert to cents
      },
    },
  };

  // For now, we'll use empty transactions array since we're focusing on the database integration
  // You can add Paddle transaction fetching later if needed
  const transactions: any[] = [];

  return (
    <>
      <div>
        <SubscriptionHeader subscription={displaySubscription} />
        <Separator className={'relative bg-border mb-8 dashboard-header-highlight'} />
      </div>
      <div className={'grid gap-6 grid-cols-1 xl:grid-cols-6'}>
        <div className={'grid auto-rows-max gap-6 grid-cols-1 xl:col-span-2'}>
          <SubscriptionNextPaymentCard transactions={transactions} subscription={displaySubscription} />
          <SubscriptionPastPaymentsCard transactions={transactions} subscriptionId={subscriptionId} />
        </div>
        <div className={'grid auto-rows-max gap-6 grid-cols-1 xl:col-span-4'}>
          <SubscriptionLineItems subscription={displaySubscription} />
        </div>
      </div>
    </>
  );
}
