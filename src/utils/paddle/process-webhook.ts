import {
  CustomerCreatedEvent,
  CustomerUpdatedEvent,
  EventEntity,
  EventName,
  SubscriptionCreatedEvent,
  SubscriptionUpdatedEvent,
} from '@paddle/paddle-node-sdk';
import { createClient } from '@/utils/supabase/server-internal';

export class ProcessWebhook {
  async processEvent(eventData: EventEntity) {
    switch (eventData.eventType) {
      case EventName.SubscriptionCreated:
      case EventName.SubscriptionUpdated:
        await this.updateSubscriptionData(eventData);
        break;
      case EventName.CustomerCreated:
      case EventName.CustomerUpdated:
        await this.updateCustomerData(eventData);
        break;
    }
  }

  private async updateSubscriptionData(eventData: SubscriptionCreatedEvent | SubscriptionUpdatedEvent) {
    const supabase = await createClient();

    try {
      // Get the price ID from the subscription
      const priceId = eventData.data.items[0].price?.id ?? '';
      console.log('Processing subscription webhook for price ID:', priceId);

      // Find the corresponding plan in your database
      const { data: plan, error: planError } = await supabase
        .from('plans')
        .select('id, interval')
        .eq('paddle_product_id', priceId)
        .single();

      if (planError || !plan) {
        console.error(`No plan found for price ID: ${priceId}`, planError);
        return;
      }

      // Get customer details from Paddle to find the user
      const { getPaddleInstance } = await import('@/utils/paddle/get-paddle-instance');
      const paddle = getPaddleInstance();
      const customer = await paddle.customers.get(eventData.data.customerId);

      if (!customer.email) {
        console.error(`No email found for customer: ${eventData.data.customerId}`);
        return;
      }

      // Find the user by email
      const { data: profile, error: profileError } = await supabase
        .from('profile')
        .select('user_id')
        .eq('email', customer.email)
        .single();

      if (profileError || !profile) {
        console.error(`No profile found for email: ${customer.email}`, profileError);
        return;
      }

      // Calculate end date based on subscription billing cycle
      const startDate = new Date(eventData.data.startedAt || eventData.data.createdAt);
      const endDate = new Date(startDate);

      // Add billing period to start date
      const billingCycle = eventData.data.items[0].price?.billingCycle;
      if (billingCycle?.interval === 'month') {
        endDate.setMonth(endDate.getMonth() + (billingCycle.frequency || 1));
      } else if (billingCycle?.interval === 'year') {
        endDate.setFullYear(endDate.getFullYear() + (billingCycle.frequency || 1));
      } else {
        // For lifetime plans or unknown intervals, set end date far in the future
        endDate.setFullYear(endDate.getFullYear() + 100);
      }

      // Upsert subscription data
      const subscriptionData = {
        user_id: profile.user_id,
        plan_id: plan.id,
        paddle_subscription_id: eventData.data.id,
        status: eventData.data.status,
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Check if subscription already exists
      const { data: existingSubscription } = await supabase
        .from('subscriptions')
        .select('id')
        .eq('paddle_subscription_id', eventData.data.id)
        .single();

      let result;
      if (existingSubscription) {
        // Update existing subscription
        result = await supabase
          .from('subscriptions')
          .update(subscriptionData)
          .eq('paddle_subscription_id', eventData.data.id)
          .select();
      } else {
        // Insert new subscription
        result = await supabase.from('subscriptions').insert(subscriptionData).select();
      }

      if (result.error) {
        console.error('Error upserting subscription:', result.error);
        throw result.error;
      }

      console.log('Successfully processed subscription webhook:', eventData.data.id);
    } catch (error) {
      console.error('Error in updateSubscriptionData:', error);
      throw error;
    }
  }

  private async updateCustomerData(eventData: CustomerCreatedEvent | CustomerUpdatedEvent) {
    const supabase = await createClient();

    try {
      console.log('Processing customer webhook for email:', eventData.data.email);

      // Update or create profile based on customer data
      // Note: We only update existing profiles, we don't create new ones
      // New profiles should be created during user signup
      const { error } = await supabase
        .from('profile')
        .update({
          // You might want to extract more data from customer if available
          firstname: eventData.data.name?.split(' ')[0] || null,
          lastname: eventData.data.name?.split(' ').slice(1).join(' ') || null,
        })
        .eq('email', eventData.data.email)
        .select();

      if (error) {
        console.error('Error updating customer profile:', error);
        // Don't throw error for customer updates as they're not critical
      } else {
        console.log('Successfully processed customer webhook:', eventData.data.id);
      }
    } catch (error) {
      console.error('Error in updateCustomerData:', error);
      // Don't throw error for customer updates as they're not critical
    }
  }
}
